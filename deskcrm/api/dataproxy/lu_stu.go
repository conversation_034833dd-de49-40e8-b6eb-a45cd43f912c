package dataproxy

import "github.com/spf13/cast"

// GetLuDataEsResp ES接口返回的原始数据结构（使用ES字段名）
type GetLuDataEsResp struct {
	// 基础信息字段 (ES字段名)
	StudentUid        int64  `json:"student_uid" mapstructure:"student_uid"`
	CourseId          int64  `json:"course_id" mapstructure:"course_id"`
	CourseName        string `json:"course_name" mapstructure:"course_name"`
	CourseStatus      int64  `json:"course_status" mapstructure:"course_status"`
	CourseType        int64  `json:"course_type" mapstructure:"course_type"`
	LessonId          int64  `json:"lesson_id" mapstructure:"lesson_id"`
	LessonName        string `json:"lesson_name" mapstructure:"lesson_name"`
	LessonStatus      int64  `json:"lesson_status" mapstructure:"lesson_status"`
	LessonType        int64  `json:"lesson_type" mapstructure:"lesson_type"`
	LessonStartTime   int64  `json:"lesson_start_time" mapstructure:"lesson_start_time"`
	LessonFinishTime  int64  `json:"lesson_finish_time" mapstructure:"lesson_finish_time"`
	LessonStopTime    int64  `json:"lesson_stop_time" mapstructure:"lesson_stop_time"`
	LessonDeleted     int64  `json:"lesson_deleted" mapstructure:"lesson_deleted"`
	UpdateTime        int64  `json:"update_time" mapstructure:"update_time"`
	Year              int64  `json:"year" mapstructure:"year"`
	AssistantUid      int64  `json:"assistant_uid" mapstructure:"assistant_uid"`
	PhotoAssistantUid int64  `json:"photo_assistant_uid" mapstructure:"photo_assistant_uid"`
	TeacherId         int64  `json:"teacher_id" mapstructure:"teacher_id"`
	AllLessonIndex    int64  `json:"all_lesson_index" mapstructure:"all_lesson_index"`
	MainLessonIndex   int64  `json:"main_lesson_index" mapstructure:"main_lesson_index"`
	BoostLessonIndex  int64  `json:"boost_lesson_index" mapstructure:"boost_lesson_index"`
	SeasonIndex       int64  `json:"season_index" mapstructure:"season_index"`
	LearnSeason       int64  `json:"learn_season" mapstructure:"learn_season"`
	Department        string `json:"department" mapstructure:"department"`
	MainDepartment    int64  `json:"main_department" mapstructure:"main_department"`
	Grades            string `json:"grades" mapstructure:"grades"`
	MainGrade         int64  `json:"main_grade" mapstructure:"main_grade"`
	Subjects          string `json:"subjects" mapstructure:"subjects"`
	MainSubject       int64  `json:"main_subject" mapstructure:"main_subject"`
	TradeStatus       int64  `json:"trade_status" mapstructure:"trade_status"`
	TradeCreateTime   int64  `json:"trade_create_time" mapstructure:"trade_create_time"`
	TradeChangeTime   int64  `json:"trade_change_time" mapstructure:"trade_change_time"`
	TradeRefundTime   int64  `json:"trade_refund_time" mapstructure:"trade_refund_time"`
	TradeChangeStatus int64  `json:"trade_change_status" mapstructure:"trade_change_status"`
	SubTradeId        int64  `json:"sub_trade_id" mapstructure:"sub_trade_id"`
	RestartId         int64  `json:"restart_id" mapstructure:"restart_id"`
	IsNeedAttend      int64  `json:"is_need_attend" mapstructure:"is_need_attend"`
	NewUserType       string `json:"new_user_type" mapstructure:"new_user_type"`
	MicNum            int64  `json:"mic_num" mapstructure:"mic_num"`
	ChatNum           int64  `json:"chat_num" mapstructure:"chat_num"`
	PreAttend         int64  `json:"pre_attend" mapstructure:"pre_attend"`

	// 到课相关字段 (ES字段名)
	Attend                  int64       `json:"attend" mapstructure:"attend"`
	AttendDuration          int64       `json:"attend_duration" mapstructure:"attend_duration"`
	AttendLong              int64       `json:"attend_long" mapstructure:"attend_long"`
	IsAttendFinish          int64       `json:"is_attend_finish" mapstructure:"is_attend_finish"`
	PreclassAttendDuration  int64       `json:"preclass_attend_duration" mapstructure:"preclass_attend_duration"`
	IsPreclassAttend        int64       `json:"is_preclass_attend" mapstructure:"is_preclass_attend"`
	IsPreclassFinishAttend  int64       `json:"is_preclass_finish_attend" mapstructure:"is_preclass_finish_attend"`
	PostclassAttendDuration int64       `json:"postclass_attend_duration" mapstructure:"postclass_attend_duration"`
	IsPostclassAttend       int64       `json:"is_postclass_attend" mapstructure:"is_postclass_attend"`
	IsPostclassFinishAttend int64       `json:"is_postclass_finish_attend" mapstructure:"is_postclass_finish_attend"`
	StudentAttendLabel      interface{} `json:"student_attend_label" mapstructure:"student_attend_label"`
	AttendDetail            interface{} `json:"attend_detail" mapstructure:"attend_detail"`
	AttendQuarter           int64       `json:"attend_quarter" mapstructure:"attend_quarter"`
	LessonRealStartTime     int64       `json:"lesson_real_start_time" mapstructure:"lesson_real_start_time"`
	InteractionAnswerDetail interface{} `json:"interaction_answer_detail" mapstructure:"interaction_answer_detail"`
	InoutCount              int64       `json:"inout_count" mapstructure:"inout_count"`
	StudentInteractionLabel interface{} `json:"student_interaction_label" mapstructure:"student_interaction_label"`

	// 录播相关字段 (ES字段名)
	PlaybackTime                          int64 `json:"playback_time" mapstructure:"playback_time"`
	PlaybackTimeIn7d                      int64 `json:"playback_time_in_7d" mapstructure:"playback_time_in_7d"`
	PlaybackTimeIn14d                     int64 `json:"playback_time_in_14d" mapstructure:"playback_time_in_14d"`
	PlaybackTimeAfterUnlock               int64 `json:"playback_time_after_unlock" mapstructure:"playback_time_after_unlock"`
	PlaybackTimeAfterUnlock7d             int64 `json:"playback_time_after_unlock_7d" mapstructure:"playback_time_after_unlock_7d"`
	IsPlaybackLongAfterUnlock7d           int64 `json:"is_playback_long_after_unlock_7d" mapstructure:"is_playback_long_after_unlock_7d"`
	IsPlaybackFinishAfterUnlock7d         int64 `json:"is_playback_finish_after_unlock_7d" mapstructure:"is_playback_finish_after_unlock_7d"`
	IsPlaybackLongAfterUnlock             int64 `json:"is_playback_long_after_unlock" mapstructure:"is_playback_long_after_unlock"`
	IsPlaybackFinishAfterUnlock           int64 `json:"is_playback_finish_after_unlock" mapstructure:"is_playback_finish_after_unlock"`
	IsNeedAttendAfterUnlock               int64 `json:"is_need_attend_after_unlock" mapstructure:"is_need_attend_after_unlock"`
	IsViewFinished                        int64 `json:"is_view_finished" mapstructure:"is_view_finished"`
	IsViewFinishIn14d                     int64 `json:"is_view_finish_in_14d" mapstructure:"is_view_finish_in_14d"`
	IsPlaybackQuarter                     int64 `json:"is_playback_quarter" mapstructure:"is_playback_quarter"`
	LessonUnlockTime                      int64 `json:"lesson_unlock_time" mapstructure:"lesson_unlock_time"`
	InclassTeacherRoomTotalPlaybackTimeV1 int64 `json:"inclass_teacher_room_total_playback_time_v1" mapstructure:"inclass_teacher_room_total_playback_time_v1"`

	// 考试答案详情 (ES字段名)
	ExamAnswer map[string]map[string]interface{} `json:"exam_answer" mapstructure:"exam_answer"`

	// LBP相关字段 (ES字段名)
	IsLbpAttend             int64       `json:"is_lbp_attend" mapstructure:"is_lbp_attend"`
	IsLbpAttendFinish       int64       `json:"is_lbp_attend_finish" mapstructure:"is_lbp_attend_finish"`
	LbpAttendDuration       int64       `json:"lbp_attend_duration" mapstructure:"lbp_attend_duration"`
	LbpLastPlaytime         int64       `json:"lbp_last_playtime" mapstructure:"lbp_last_playtime"`
	LbpInteractionRightNum  int64       `json:"lbp_interaction_right_num" mapstructure:"lbp_interaction_right_num"`
	LbpInteractionTotalNum  int64       `json:"lbp_interaction_total_num" mapstructure:"lbp_interaction_total_num"`
	LbpInteractionSubmitNum int64       `json:"lbp_interaction_submit_num" mapstructure:"lbp_interaction_submit_num"`
	LbpOnlineDuration       int64       `json:"lbp_online_duration" mapstructure:"lbp_online_duration"`
	LbpPlayDetail           interface{} `json:"lbp_play_detail" mapstructure:"lbp_play_detail"`

	// 小灶课相关字段 (ES字段名)
	IsAssistantcourseAttend int64 `json:"is_assistantcourse_attend" mapstructure:"is_assistantcourse_attend"`
	IsAssistantcourseFinish int64 `json:"is_assistantcourse_finish" mapstructure:"is_assistantcourse_finish"`

	// 浣熊英语相关字段 (ES字段名)
	HxPretestFinishnum int64 `json:"hx_pretest_finishnum" mapstructure:"hx_pretest_finishnum"`
	HxAlltestFinishnum int64 `json:"hx_alltest_finishnum" mapstructure:"hx_alltest_finishnum"`
}

type GetLuDataResp struct {
	// ==================== 基础信息模块 (Basic Info) ====================
	StudentUid       int64  `json:"studentUid"`       // 学生UID
	CourseId         int64  `json:"courseId"`         // 课程ID
	CourseName       string `json:"courseName"`       // 课程名称
	CourseStatus     int64  `json:"courseStatus"`     // 课程状态
	CourseType       int64  `json:"courseType"`       // 课程类型
	LessonId         int64  `json:"lessonId"`         // 课节ID
	LessonName       string `json:"lessonName"`       // 课节名称
	LessonStatus     int64  `json:"lessonStatus"`     // 课节状态
	LessonType       int64  `json:"lessonType"`       // 课节类型
	LessonStartTime  int64  `json:"lessonStartTime"`  // 课节开始时间
	LessonFinishTime int64  `json:"lessonFinishTime"` // 课节结束时间
	LessonStopTime   int64  `json:"lessonStopTime"`   // 课节停止时间
	LessonDeleted    int64  `json:"lessonDeleted"`    // 课节是否删除
	UpdateTime       int64  `json:"updateTime"`       // 更新时间
	Year             int64  `json:"year"`             // 年份

	// 班主任和教师信息
	AssistantUid      int64 `json:"assistantUid"`      // 班主任UID
	PhotoAssistantUid int64 `json:"photoAssistantUid"` // 拍照班主任UID
	TeacherId         int64 `json:"teacherId"`         // 教师ID

	// 课程层级信息
	AllLessonIndex   int64 `json:"allLessonIndex"`   // 全部课节索引
	MainLessonIndex  int64 `json:"mainLessonIndex"`  // 主课节索引
	BoostLessonIndex int64 `json:"boostLessonIndex"` // 提升课节索引
	SeasonIndex      int64 `json:"seasonIndex"`      // 学季索引
	LearnSeason      int64 `json:"learnSeason"`      // 学习学季

	// 学科和年级信息
	Department     string `json:"department"`     // 部门
	MainDepartment int64  `json:"mainDepartment"` // 主部门
	Grades         string `json:"grades"`         // 年级
	MainGrade      int64  `json:"mainGrade"`      // 主年级
	Subjects       string `json:"subjects"`       // 学科
	MainSubject    int64  `json:"mainSubject"`    // 主学科

	// 交易相关信息
	TradeStatus       int64 `json:"tradeStatus"`       // 交易状态
	TradeCreateTime   int64 `json:"tradeCreateTime"`   // 交易创建时间
	TradeChangeTime   int64 `json:"tradeChangeTime"`   // 交易变更时间
	TradeRefundTime   int64 `json:"tradeRefundTime"`   // 交易退款时间
	TradeChangeStatus int64 `json:"tradeChangeStatus"` // 交易变更状态
	SubTradeId        int64 `json:"subTradeId"`        // 子交易ID
	RestartId         int64 `json:"restartId"`         // 重启ID

	// 其他基础信息
	IsNeedAttend int64  `json:"isNeedAttend"` // 是否需要到课
	NewUserType  string `json:"newUserType"`  // 新用户类型
	MicNum       int64  `json:"micNum"`       // 麦克风次数
	ChatNum      int64  `json:"chatNum"`      // 聊天次数
	PreAttend    int64  `json:"preAttend"`    // 预到课

	// ==================== 到课相关模块 (Attendance) ====================
	Attend         int64 `json:"attend"`         // 是否到课
	AttendDuration int64 `json:"attendDuration"` // 到课时长
	AttendLong     int64 `json:"attendLong"`     // 是否到课30分钟
	IsAttendFinish int64 `json:"isAttendFinish"` // 是否完课

	// 课前到课
	PreclassAttendDuration int64 `json:"preclassAttendDuration"` // 课前到课时长
	IsPreclassAttend       int64 `json:"isPreclassAttend"`       // 课前是否到课达标
	IsPreclassFinishAttend int64 `json:"isPreclassFinishAttend"` // 课前是否完课

	// 课后到课
	PostclassAttendDuration int64 `json:"postclassAttendDuration"` // 课后到课时长
	IsPostclassAttend       int64 `json:"isPostclassAttend"`       // 课后是否到课达标
	IsPostclassFinishAttend int64 `json:"isPostclassFinishAttend"` // 课后是否完课

	// 到课标签和概况
	AttendLabel    interface{} `json:"attendLabel"`    // 到课标签
	AttendOverview interface{} `json:"attendOverview"` // 课中行为概况
	AttendQuarter  int64       `json:"attendQuarter"`  // 专题课是否到课四分之一

	// 上课表现相关
	LessonRealStartTime     int64       `json:"lessonRealStartTime"`     // 章节实际开课时间
	InteractionAnswerDetail interface{} `json:"interactionAnswerDetail"` // 口述题行为明细
	InoutCount              int64       `json:"inoutCount"`              // 课中进出教室次数
	InteractionLabel        interface{} `json:"interactionLabel"`        // 口述题行为标签

	// ==================== 录播相关模块 (Playback) ====================
	PlaybackTotalTime                     int64   `json:"playbackTotalTime"`                           // 录播总时长
	PlaybackTimeIn7d                      int64   `json:"playbackTimeIn7d"`                            // 7天内录播时长
	PlaybackTimeIn14d                     int64   `json:"playbackTimeIn14d"`                           // 14天内录播时长
	PlaybackTimeAfterUnlock               int64   `json:"playbackTimeAfterUnlock"`                     // 解锁后录播时长
	PlaybackTimeAfterUnlock7d             int64   `json:"playbackTimeAfterUnlock7d"`                   // 解锁后7天内录播时长
	IsPlaybackLongAfterUnlock7d           int64   `json:"isPlaybackLongAfterUnlock7d"`                 // 录播是否7天内观看
	IsPlaybackFinishAfterUnlock7d         int64   `json:"isPlaybackFinishAfterUnlock7d"`               // 录播是否7天内完成观看
	IsPlaybackLongAfterUnlock             int64   `json:"isPlaybackLongAfterUnlock"`                   // 解锁后到课回放完成
	IsPlaybackFinishAfterUnlock           int64   `json:"isPlaybackFinishAfterUnlock"`                 // 解锁后完课回放完成
	IsNeedAttendAfterUnlock               int64   `json:"isNeedAttendAfterUnlock"`                     // 解锁后是否应到课
	IsViewFinished                        int64   `json:"isViewFinished"`                              // 到课完课状态
	IsViewFinishIn14d                     int64   `json:"isViewFinishIn14d"`                           // 初中到课完课状态
	IsPlaybackQuarter                     int64   `json:"isPlaybackQuarter"`                           // 专题课是否回放四分之一
	LessonUnlockTime                      int64   `json:"lessonUnlockTime"`                            // 解锁时间
	PlaybackAttendLabel                   []int64 `json:"playbackAttendLabel"`                         // 回放到课标签
	PlaybackInteractionLabel              []int64 `json:"playbackInteractionLabel"`                    // 回放互动标签
	InclassTeacherRoomTotalPlaybackTimeV1 int64   `json:"inclass_teacher_room_total_playback_time_v1"` // 回放时长(新)

	// ==================== 考试相关模块 (Exams) ====================
	ExamAnswer map[string]map[string]interface{} `json:"exam_answer"` // 考试答案详情

	// 互动题 (exam1)
	IsExerciseSubmit       int64 `json:"isExerciseSubmit"`       // 互动题是否提交
	ExerciseRightNum       int64 `json:"exerciseRightNum"`       // 互动题正确数
	ExerciseParticipateNum int64 `json:"exerciseParticipateNum"` // 互动题参与数
	ExerciseTotalNum       int64 `json:"exerciseTotalNum"`       // 互动题总数
	PlaybackAllNum         int64 `json:"playbackAllNum"`         // 回放总题数
	PlaybackParticipateNum int64 `json:"playbackParticipateNum"` // 回放参与数
	PlaybackRightNum       int64 `json:"playbackRightNum"`       // 回放正确数

	// 课前预习 (exam5)
	IsPreviewFinish5       int64 `json:"isPreviewFinish5"`       // 课前预习是否完成
	PreviewFinishTime5     int64 `json:"previewFinishTime5"`     // 课前预习完成时间
	PreviewParticipateNum5 int64 `json:"previewParticipateNum5"` // 课前预习参与数
	PreviewCorrectNum5     int64 `json:"previewCorrectNum5"`     // 课前预习正确数
	PreviewTotalNum5       int64 `json:"previewTotalNum5"`       // 课前预习总数
	PreviewIsExpound5      int64 `json:"previewIsExpound5"`      // 课前预习是否讲解

	// 通用预习字段 (兼容旧版本)
	IsPreviewFinish       int64 `json:"isPreviewFinish"`       // 预习是否完成
	PreviewFinishTime     int64 `json:"previewFinishTime"`     // 预习完成时间
	PreviewParticipateNum int64 `json:"previewParticipateNum"` // 预习参与数
	PreviewCorrectNum     int64 `json:"previewCorrectNum"`     // 预习正确数
	PreviewTotalNum       int64 `json:"previewTotalNum"`       // 预习总数
	PreviewIsExpound      int64 `json:"previewIsExpound"`      // 预习是否讲解

	// 巩固练习 (exam7)
	IsHomeworkSubmit               int64 `json:"isHomeworkSubmit"`               // 巩固练习是否提交
	HomeworkFinishTime             int64 `json:"homeworkFinishTime"`             // 巩固练习完成时间
	HomeworkLastSubmitTime         int64 `json:"homeworkLastSubmitTime"`         // 巩固练习最后提交时间
	HomeworkPracticeParticipateNum int64 `json:"homeworkPracticeParticipateNum"` // 巩固练习参与数
	HomeworkPracticeCorrectNum     int64 `json:"homeworkPracticeCorrectNum"`     // 巩固练习正确数
	HomeworkPracticeTotalNum       int64 `json:"homeworkPracticeTotalNum"`       // 巩固练习总数
	HomeworkLevel                  int64 `json:"homeworkLevel"`                  // 巩固练习等级
	HomeworkLastCorrectTime        int64 `json:"homeworkLastCorrectTime"`        // 巩固练习最后批改时间
	HomeworkSubmissions            int64 `json:"homeworkSubmissions"`            // 巩固练习提交次数
	HomeworkIsExpound              int64 `json:"homeworkIsExpound"`              // 巩固练习是否讲解
	HomeworkIsAmend                int64 `json:"homeworkIsAmend"`                // 巩固练习是否修正
	IsViewHomeworkExpoundVideo     int64 `json:"isViewHomeworkExpoundVideo"`     // 是否观看原版错题视频

	// ilab巩固练习 (exam31)
	IlabHomeworkGeXingTotalNum     int64 `json:"ilabHomeworkGeXingTotalNum"`     // 必做卷个性题总数
	IlabHomeworkMustRightNum       int64 `json:"ilabHomeworkMustRightNum"`       // 必做卷正确数
	IlabHomeworkMustIsSubmit       int64 `json:"ilabHomeworkMustIsSubmit"`       // 必做卷是否提交
	IlabHomeworkMustSubmitTime     int64 `json:"ilabHomeworkMustSubmitTime"`     // 必做卷提交时间
	IlabHomeworkMustParticipateNum int64 `json:"ilabHomeworkMustParticipateNum"` // 必做卷参与数

	// 阶段测 (exam9)
	IsStageTestExamSubmit       int64 `json:"isStageTestExamSubmit"`       // 阶段测是否提交
	StageTestExamFinishTime     int64 `json:"stageTestExamFinishTime"`     // 阶段测完成时间
	StageTestExamParticipateNum int64 `json:"stageTestExamParticipateNum"` // 阶段测参与数
	StageTestExamCorrectNum     int64 `json:"stageTestExamCorrectNum"`     // 阶段测正确数
	StageTestExamTotalNum       int64 `json:"stageTestExamTotalNum"`       // 阶段测总数
	StageTestIsExpound          int64 `json:"stageTestIsExpound"`          // 阶段测是否讲解
	StageTestIsCorrect          int64 `json:"stageTestIsCorrect"`          // 阶段测是否批改
	StageTestCorrectLevel       int64 `json:"stageTestCorrectLevel"`       // 阶段测批改等级
	StageTestCorrectTime        int64 `json:"stageTestCorrectTime"`        // 阶段测批改时间

	// 堂堂测 (exam10)
	IsTangTangExamSubmit       int64 `json:"isTangTangExamSubmit"`       // 堂堂测是否提交
	TangTangExamFinishTime     int64 `json:"tangTangExamFinishTime"`     // 堂堂测完成时间
	TangTangExamParticipateNum int64 `json:"tangTangExamParticipateNum"` // 堂堂测参与数
	TangTangExamCorrectNum     int64 `json:"tangTangExamCorrectNum"`     // 堂堂测正确数
	TangTangExamTotalNum       int64 `json:"tangTangExamTotalNum"`       // 堂堂测总数
	TangTangIsExpound          int64 `json:"tangTangIsExpound"`          // 堂堂测是否讲解
	TangTangExamScore          int64 `json:"tangTangExamScore"`          // 堂堂测分数

	// 同步练习 (exam11)
	IsSynchronousPracticeSubmit       int64 `json:"isSynchronousPracticeSubmit"`       // 同步练习是否提交
	SynchronousPracticeParticipateNum int64 `json:"synchronousPracticeParticipateNum"` // 同步练习参与数
	SynchronousPracticeCorrectNum     int64 `json:"synchronousPracticeCorrectNum"`     // 同步练习正确数
	SynchronousPracticeTotalNum       int64 `json:"synchronousPracticeTotalNum"`       // 同步练习总数

	// 初高中预习 (exam13)
	PreviewIsSubmit13       int64 `json:"previewIsSubmit13"`       // 初高中预习是否提交
	IsPreviewFinish13       int64 `json:"isPreviewFinish13"`       // 初高中预习是否完成
	PreviewFinishTime13     int64 `json:"previewFinishTime13"`     // 初高中预习完成时间
	PreviewParticipateNum13 int64 `json:"previewParticipateNum13"` // 初高中预习参与数
	PreviewCorrectNum13     int64 `json:"previewCorrectNum13"`     // 初高中预习正确数
	PreviewTotalNum13       int64 `json:"previewTotalNum13"`       // 初高中预习总数

	// 口述题 (exam32)
	OralQuestionTotalNum       int64 `json:"oralQuestionTotalNum"`       // 口述题题目总数
	OralQuestionSubmit         int64 `json:"oralQuestionSubmit"`         // 口述题是否提交
	OralQuestionParticipateNum int64 `json:"oralQuestionParticipateNum"` // 口述题参与题目数
	OralQuestionCorrectNum     int64 `json:"oralQuestionCorrectNum"`     // 口述题答对题目数
	OralQuestionSubmitTime     int64 `json:"oralQuestionSubmitTime"`     // 口述题提交时间
	OralQuestionCorrectTime    int64 `json:"oralQuestionCorrectTime"`    // 口述题批改时间

	// 内部阶段测 (exam34)
	InnerStageIsSubmit     int64       `json:"innerStageIsSubmit"`     // 内部阶段测是否提交
	InnerStageHighestScore int64       `json:"innerStageHighestScore"` // 内部阶段测最高分
	InnerStageIsPass       int64       `json:"innerStageIsPass"`       // 内部阶段测是否通过
	InnerStageIsFullScore  int64       `json:"innerStageIsFullScore"`  // 内部阶段测是否满分
	InnerStageAnswerDetail interface{} `json:"innerStageAnswerDetail"` // 内部阶段测答题详情

	// ==================== 英语专项模块 (English) ====================
	// 帮帮英语 - 单词练习 (exam14)
	WordpracticeParticipateNum int64 `json:"wordpracticeParticipateNum"` // 单词练习参与数
	WordpracticeRightNum       int64 `json:"wordpracticeRightNum"`       // 单词练习正确数
	WordpracticeTotalNum       int64 `json:"wordpracticeTotalNum"`       // 单词练习总数

	// 帮帮英语 - 单词学习 (exam15)
	WordlearnParticipateNum int64 `json:"wordlearnParticipateNum"` // 单词学习参与数
	WordlearnRightNum       int64 `json:"wordlearnRightNum"`       // 单词学习正确数
	WordlearnTotalNum       int64 `json:"wordlearnTotalNum"`       // 单词学习总数

	// 帮帮英语 - 出门测 (exam18)
	OutTestParticipateNum int64 `json:"outTestParticipateNum"` // 出门测参与数
	OutTestRightNum       int64 `json:"outTestRightNum"`       // 出门测正确数
	OutTestTotalNum       int64 `json:"outTestTotalNum"`       // 出门测总数

	// 帮帮英语 - 提升训练 (exam19)
	ImproveParticipateNum int64 `json:"improveParticipateNum"` // 提升训练参与数
	ImproveRightNum       int64 `json:"improveRightNum"`       // 提升训练正确数
	ImproveTotalNum       int64 `json:"improveTotalNum"`       // 提升训练总数

	// 帮帮英语 - 语法练习 (exam21)
	GrammarpracticeParticipateNum int64 `json:"grammarpracticeParticipateNum"` // 语法练习参与数
	GrammarpracticeRightNum       int64 `json:"grammarpracticeRightNum"`       // 语法练习正确数
	GrammarpracticeTotalNum       int64 `json:"grammarpracticeTotalNum"`       // 语法练习总数

	// 帮帮英语 - 练一练 (exam17)
	PracticesParticipateNum int64 `json:"practicesParticipateNum"` // 练一练参与数
	PracticesRightNum       int64 `json:"practicesRightNum"`       // 练一练正确数
	PracticesTotalNum       int64 `json:"practicesTotalNum"`       // 练一练总数

	// 帮帮英语 - 入门测 (exam23)
	InTestParticipateNum int64 `json:"inTestParticipateNum"` // 入门测参与数
	InTestRightNum       int64 `json:"inTestRightNum"`       // 入门测正确数
	InTestTotalNum       int64 `json:"inTestTotalNum"`       // 入门测总数

	// 浣熊英语 - 小试牛刀 (exam211)
	TryKnifeScore        int64 `json:"tryKnifeScore"`        // 小试牛刀分数
	TryKnifeSubmitTime   int64 `json:"tryKnifeSubmitTime"`   // 小试牛刀提交时间
	TryKnifeSubmitStatus int64 `json:"tryKnifeSubmitStatus"` // 小试牛刀提交状态

	// 浣熊英语 - lesson1 (exam212)
	LessonOneScore        int64 `json:"lessonOneScore"`        // lesson1分数
	LessonOneSubmitTime   int64 `json:"lessonOneSubmitTime"`   // lesson1提交时间
	LessonOneSubmitStatus int64 `json:"lessonOneSubmitStatus"` // lesson1提交状态

	// 浣熊英语 - lesson2 (exam212)
	LessonTwoScore        int64 `json:"lessonTwoScore"`        // lesson2分数
	LessonTwoSubmitTime   int64 `json:"lessonTwoSubmitTime"`   // lesson2提交时间
	LessonTwoSubmitStatus int64 `json:"lessonTwoSubmitStatus"` // lesson2提交状态

	// 浣熊英语 - lesson3 (exam212)
	LessonThreeScore        int64 `json:"lessonThreeScore"`        // lesson3分数
	LessonThreeSubmitTime   int64 `json:"lessonThreeSubmitTime"`   // lesson3提交时间
	LessonThreeSubmitStatus int64 `json:"lessonThreeSubmitStatus"` // lesson3提交状态

	// 浣熊英语 - 综合演练 (exam216)
	SynthesisManoeuvreScore        int64 `json:"synthesisManoeuvreScore"`        // 综合演练分数
	SynthesisManoeuvreSubmitTime   int64 `json:"synthesisManoeuvreSubmitTime"`   // 综合演练提交时间
	SynthesisManoeuvreSubmitStatus int64 `json:"synthesisManoeuvreSubmitStatus"` // 综合演练提交状态

	// 浣熊英语 - 能力挑战 (exam213)
	PowerChallengeScore        int64 `json:"powerChallengeScore"`        // 能力挑战分数
	PowerChallengeSubmitTime   int64 `json:"powerChallengeSubmitTime"`   // 能力挑战提交时间
	PowerChallengeSubmitStatus int64 `json:"powerChallengeSubmitStatus"` // 能力挑战提交状态

	// 浣熊英语 - 综合秀场 (exam217)
	SynthesisShowScore        int64 `json:"synthesisShowScore"`        // 综合秀场分数
	SynthesisShowSubmitTime   int64 `json:"synthesisShowSubmitTime"`   // 综合秀场提交时间
	SynthesisShowSubmitStatus int64 `json:"synthesisShowSubmitStatus"` // 综合秀场提交状态

	// 浣熊英语 - 测试完成数量
	HxPretestFinishnum int64 `json:"hxPretestFinishnum"` // 浣熊课前测试题的完成数量
	HxAlltestAinishnum int64 `json:"hxAlltestAinishnum"` // 浣熊全部测试题的完成数量

	// ==================== LBP相关模块 (LBP) ====================
	IsLbpAttend                     int64       `json:"isLbpAttend"`                     // LBP到课
	IsLbpAttendFinish               int64       `json:"isLbpAttendFinish"`               // LBP完课
	LbpAttendDuration               int64       `json:"lbpAttendDuration"`               // LBP观看时长
	LbpLastPlaytime                 int64       `json:"lbpLastPlaytime"`                 // LBP最后一次观看时间
	LbpInteractionRightNum          int64       `json:"lbpInteractionRightNum"`          // LBP课中互动题正确数
	LbpInteractionTotalNum          int64       `json:"lbpInteractionTotalNum"`          // LBP课中互动题总题数
	LbpInteractionSubmitNum         int64       `json:"lbpInteractionSubmitNum"`         // LBP课中互动题提交数
	LbpOnlineDuration               int64       `json:"lbpOnlineDuration"`               // LBP累积在线时长
	LbpPlayDetail                   interface{} `json:"lbpPlayDetail"`                   // 学生进出直播间明细
	MixLiveInteractionRightNum      int64       `json:"mixLiveInteractionRightNum"`      // LBP互动题正确数
	MixLiveInteractionSubmitNum     int64       `json:"mixLiveInteractionSubmitNum"`     // LBP互动题提交数
	MixPlaybackInteractionRightNum  int64       `json:"mixPlaybackInteractionRightNum"`  // 融合回放互动题正确数
	MixPlaybackInteractionSubmitNum int64       `json:"mixPlaybackInteractionSubmitNum"` // 融合回放互动题提交数

	// ==================== 其他功能模块 (Others) ====================
	// 小灶课
	IsAssistantcourseAttend int64 `json:"isAssistantcourseAttend"` // 小灶课到课状态（>1/3）
	IsAssistantcourseFinish int64 `json:"isAssistantcourseFinish"` // 小灶课完课状态（>2/3)

	// 兼容字段 (用于向后兼容)
	HaveOralQuestion int64 `json:"haveOralQuestion"` // 是否有口述题 (已废弃，保留兼容性)
}

// ConvertEsRespToCommonResp 将ES返回的原始数据转换为通用响应格式
func ConvertEsRespToCommonResp(esResp *GetLuDataEsResp) *GetLuDataResp {
	if esResp == nil {
		return nil
	}

	resp := &GetLuDataResp{
		// 基础信息字段映射
		StudentUid:        esResp.StudentUid,
		CourseId:          esResp.CourseId,
		CourseName:        esResp.CourseName,
		CourseStatus:      esResp.CourseStatus,
		CourseType:        esResp.CourseType,
		LessonId:          esResp.LessonId,
		LessonName:        esResp.LessonName,
		LessonStatus:      esResp.LessonStatus,
		LessonType:        esResp.LessonType,
		LessonStartTime:   esResp.LessonStartTime,
		LessonFinishTime:  esResp.LessonFinishTime,
		LessonStopTime:    esResp.LessonStopTime,
		LessonDeleted:     esResp.LessonDeleted,
		UpdateTime:        esResp.UpdateTime,
		Year:              esResp.Year,
		AssistantUid:      esResp.AssistantUid,
		PhotoAssistantUid: esResp.PhotoAssistantUid,
		TeacherId:         esResp.TeacherId,
		AllLessonIndex:    esResp.AllLessonIndex,
		MainLessonIndex:   esResp.MainLessonIndex,
		BoostLessonIndex:  esResp.BoostLessonIndex,
		SeasonIndex:       esResp.SeasonIndex,
		LearnSeason:       esResp.LearnSeason,
		Department:        esResp.Department,
		MainDepartment:    esResp.MainDepartment,
		Grades:            esResp.Grades,
		MainGrade:         esResp.MainGrade,
		Subjects:          esResp.Subjects,
		MainSubject:       esResp.MainSubject,
		TradeStatus:       esResp.TradeStatus,
		TradeCreateTime:   esResp.TradeCreateTime,
		TradeChangeTime:   esResp.TradeChangeTime,
		TradeRefundTime:   esResp.TradeRefundTime,
		TradeChangeStatus: esResp.TradeChangeStatus,
		SubTradeId:        esResp.SubTradeId,
		RestartId:         esResp.RestartId,
		IsNeedAttend:      esResp.IsNeedAttend,
		NewUserType:       esResp.NewUserType,
		MicNum:            esResp.MicNum,
		ChatNum:           esResp.ChatNum,
		PreAttend:         esResp.PreAttend,

		// 到课相关字段映射
		Attend:                  esResp.Attend,
		AttendDuration:          esResp.AttendDuration,
		AttendLong:              esResp.AttendLong,
		IsAttendFinish:          esResp.IsAttendFinish,
		PreclassAttendDuration:  esResp.PreclassAttendDuration,
		IsPreclassAttend:        esResp.IsPreclassAttend,
		IsPreclassFinishAttend:  esResp.IsPreclassFinishAttend,
		PostclassAttendDuration: esResp.PostclassAttendDuration,
		IsPostclassAttend:       esResp.IsPostclassAttend,
		IsPostclassFinishAttend: esResp.IsPostclassFinishAttend,
		AttendLabel:             esResp.StudentAttendLabel, // ES字段名映射
		AttendOverview:          esResp.AttendDetail,       // ES字段名映射
		AttendQuarter:           esResp.AttendQuarter,
		LessonRealStartTime:     esResp.LessonRealStartTime,
		InteractionAnswerDetail: esResp.InteractionAnswerDetail,
		InoutCount:              esResp.InoutCount,
		InteractionLabel:        esResp.StudentInteractionLabel, // ES字段名映射

		// 录播相关字段映射
		PlaybackTotalTime:                     esResp.PlaybackTime, // ES字段名映射
		PlaybackTimeIn7d:                      esResp.PlaybackTimeIn7d,
		PlaybackTimeIn14d:                     esResp.PlaybackTimeIn14d,
		PlaybackTimeAfterUnlock:               esResp.PlaybackTimeAfterUnlock,
		PlaybackTimeAfterUnlock7d:             esResp.PlaybackTimeAfterUnlock7d,
		IsPlaybackLongAfterUnlock7d:           esResp.IsPlaybackLongAfterUnlock7d,
		IsPlaybackFinishAfterUnlock7d:         esResp.IsPlaybackFinishAfterUnlock7d,
		IsPlaybackLongAfterUnlock:             esResp.IsPlaybackLongAfterUnlock,
		IsPlaybackFinishAfterUnlock:           esResp.IsPlaybackFinishAfterUnlock,
		IsNeedAttendAfterUnlock:               esResp.IsNeedAttendAfterUnlock,
		IsViewFinished:                        esResp.IsViewFinished,
		IsViewFinishIn14d:                     esResp.IsViewFinishIn14d,
		IsPlaybackQuarter:                     esResp.IsPlaybackQuarter,
		LessonUnlockTime:                      esResp.LessonUnlockTime,
		InclassTeacherRoomTotalPlaybackTimeV1: esResp.InclassTeacherRoomTotalPlaybackTimeV1,

		// 考试相关字段映射
		ExamAnswer: esResp.ExamAnswer,

		// LBP相关字段映射
		IsLbpAttend:             esResp.IsLbpAttend,
		IsLbpAttendFinish:       esResp.IsLbpAttendFinish,
		LbpAttendDuration:       esResp.LbpAttendDuration,
		LbpLastPlaytime:         esResp.LbpLastPlaytime,
		LbpInteractionRightNum:  esResp.LbpInteractionRightNum,
		LbpInteractionTotalNum:  esResp.LbpInteractionTotalNum,
		LbpInteractionSubmitNum: esResp.LbpInteractionSubmitNum,
		LbpOnlineDuration:       esResp.LbpOnlineDuration,
		LbpPlayDetail:           esResp.LbpPlayDetail,

		// 小灶课相关字段映射
		IsAssistantcourseAttend: esResp.IsAssistantcourseAttend,
		IsAssistantcourseFinish: esResp.IsAssistantcourseFinish,

		// 浣熊英语相关字段映射
		HxPretestFinishnum: esResp.HxPretestFinishnum,
		HxAlltestAinishnum: esResp.HxAlltestFinishnum, // 注意字段名差异
	}

	// 从ExamAnswer中提取具体的考试字段
	if esResp.ExamAnswer != nil {
		resp.extractExamFields(esResp.ExamAnswer)
	}

	return resp
}

// extractExamFields 从ExamAnswer中提取具体的考试字段
func (resp *GetLuDataResp) extractExamFields(examAnswer map[string]map[string]interface{}) {
	// 互动题 (exam1)
	if exam1, ok := examAnswer["exam1"]; ok {
		if val, exists := exam1["is_submit"]; exists {
			resp.IsExerciseSubmit = cast.ToInt64(val)
		}
		if val, exists := exam1["right_num"]; exists {
			resp.ExerciseRightNum = cast.ToInt64(val)
		}
		if val, exists := exam1["participate_num"]; exists {
			resp.ExerciseParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam1["total_num"]; exists {
			resp.ExerciseTotalNum = cast.ToInt64(val)
		}
	}

	// 课前预习 (exam5)
	if exam5, ok := examAnswer["exam5"]; ok {
		if val, exists := exam5["is_finish"]; exists {
			resp.IsPreviewFinish5 = cast.ToInt64(val)
		}
		if val, exists := exam5["finish_time"]; exists {
			resp.PreviewFinishTime5 = cast.ToInt64(val)
		}
		if val, exists := exam5["participate_num"]; exists {
			resp.PreviewParticipateNum5 = cast.ToInt64(val)
		}
		if val, exists := exam5["right_num"]; exists {
			resp.PreviewCorrectNum5 = cast.ToInt64(val)
		}
		if val, exists := exam5["total_num"]; exists {
			resp.PreviewTotalNum5 = cast.ToInt64(val)
		}
		if val, exists := exam5["is_expound"]; exists {
			resp.PreviewIsExpound5 = cast.ToInt64(val)
		}
	}

	// 巩固练习 (exam7)
	if exam7, ok := examAnswer["exam7"]; ok {
		if val, exists := exam7["is_submit"]; exists {
			resp.IsHomeworkSubmit = cast.ToInt64(val)
		}
		if val, exists := exam7["submit_time"]; exists {
			resp.HomeworkFinishTime = cast.ToInt64(val)
		}
		if val, exists := exam7["last_submit_time"]; exists {
			resp.HomeworkLastSubmitTime = cast.ToInt64(val)
		}
		if val, exists := exam7["participate_num"]; exists {
			resp.HomeworkPracticeParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam7["right_num"]; exists {
			resp.HomeworkPracticeCorrectNum = cast.ToInt64(val)
		}
		if val, exists := exam7["total_num"]; exists {
			resp.HomeworkPracticeTotalNum = cast.ToInt64(val)
		}
		if val, exists := exam7["correct_level"]; exists {
			resp.HomeworkLevel = cast.ToInt64(val)
		}
		if val, exists := exam7["last_correct_time"]; exists {
			resp.HomeworkLastCorrectTime = cast.ToInt64(val)
		}
		if val, exists := exam7["submit_num"]; exists {
			resp.HomeworkSubmissions = cast.ToInt64(val)
		}
		if val, exists := exam7["is_expound"]; exists {
			resp.HomeworkIsExpound = cast.ToInt64(val)
		}
		if val, exists := exam7["is_amend"]; exists {
			resp.HomeworkIsAmend = cast.ToInt64(val)
		}
		if val, exists := exam7["is_view_wrong_expound_video"]; exists {
			resp.IsViewHomeworkExpoundVideo = cast.ToInt64(val)
		}
	}

	// 堂堂测 (exam10)
	if exam10, ok := examAnswer["exam10"]; ok {
		if val, exists := exam10["is_submit"]; exists {
			resp.IsTangTangExamSubmit = cast.ToInt64(val)
		}
		if val, exists := exam10["submit_time"]; exists {
			resp.TangTangExamFinishTime = cast.ToInt64(val)
		}
		if val, exists := exam10["participate_num"]; exists {
			resp.TangTangExamParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam10["right_num"]; exists {
			resp.TangTangExamCorrectNum = cast.ToInt64(val)
		}
		if val, exists := exam10["total_num"]; exists {
			resp.TangTangExamTotalNum = cast.ToInt64(val)
		}
		if val, exists := exam10["is_expound"]; exists {
			resp.TangTangIsExpound = cast.ToInt64(val)
		}
		if val, exists := exam10["answer_score"]; exists {
			resp.TangTangExamScore = cast.ToInt64(val)
		}
	}

	// 口述题 (exam32)
	if exam32, ok := examAnswer["exam32"]; ok {
		if val, exists := exam32["total_num"]; exists {
			resp.OralQuestionTotalNum = cast.ToInt64(val)
		}
		if val, exists := exam32["is_submit"]; exists {
			resp.OralQuestionSubmit = cast.ToInt64(val)
		}
		if val, exists := exam32["participate_num"]; exists {
			resp.OralQuestionParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam32["right_num"]; exists {
			resp.OralQuestionCorrectNum = cast.ToInt64(val)
		}
		if val, exists := exam32["submit_time"]; exists {
			resp.OralQuestionSubmitTime = cast.ToInt64(val)
		}
		if val, exists := exam32["correct_time"]; exists {
			resp.OralQuestionCorrectTime = cast.ToInt64(val)
		}
	}

	// 阶段测 (exam9)
	if exam9, ok := examAnswer["exam9"]; ok {
		if val, exists := exam9["is_submit"]; exists {
			resp.IsStageTestExamSubmit = cast.ToInt64(val)
		}
		if val, exists := exam9["submit_time"]; exists {
			resp.StageTestExamFinishTime = cast.ToInt64(val)
		}
		if val, exists := exam9["participate_num"]; exists {
			resp.StageTestExamParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam9["right_num"]; exists {
			resp.StageTestExamCorrectNum = cast.ToInt64(val)
		}
		if val, exists := exam9["total_num"]; exists {
			resp.StageTestExamTotalNum = cast.ToInt64(val)
		}
		if val, exists := exam9["is_expound"]; exists {
			resp.StageTestIsExpound = cast.ToInt64(val)
		}
		if val, exists := exam9["is_correct"]; exists {
			resp.StageTestIsCorrect = cast.ToInt64(val)
		}
		if val, exists := exam9["correct_level"]; exists {
			resp.StageTestCorrectLevel = cast.ToInt64(val)
		}
		if val, exists := exam9["correct_time"]; exists {
			resp.StageTestCorrectTime = cast.ToInt64(val)
		}
	}

	// 同步练习 (exam11)
	if exam11, ok := examAnswer["exam11"]; ok {
		if val, exists := exam11["is_submit"]; exists {
			resp.IsSynchronousPracticeSubmit = cast.ToInt64(val)
		}
		if val, exists := exam11["participate_num"]; exists {
			resp.SynchronousPracticeParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam11["right_num"]; exists {
			resp.SynchronousPracticeCorrectNum = cast.ToInt64(val)
		}
		if val, exists := exam11["total_num"]; exists {
			resp.SynchronousPracticeTotalNum = cast.ToInt64(val)
		}
	}

	// 初高中预习 (exam13)
	if exam13, ok := examAnswer["exam13"]; ok {
		if val, exists := exam13["is_submit"]; exists {
			resp.PreviewIsSubmit13 = cast.ToInt64(val)
		}
		if val, exists := exam13["is_finish"]; exists {
			resp.IsPreviewFinish13 = cast.ToInt64(val)
		}
		if val, exists := exam13["finish_time"]; exists {
			resp.PreviewFinishTime13 = cast.ToInt64(val)
		}
		if val, exists := exam13["participate_num"]; exists {
			resp.PreviewParticipateNum13 = cast.ToInt64(val)
		}
		if val, exists := exam13["right_num"]; exists {
			resp.PreviewCorrectNum13 = cast.ToInt64(val)
		}
		if val, exists := exam13["total_num"]; exists {
			resp.PreviewTotalNum13 = cast.ToInt64(val)
		}
	}

	// ilab巩固练习 (exam31)
	if exam31, ok := examAnswer["exam31"]; ok {
		if val, exists := exam31["must_gexing_total_num"]; exists {
			resp.IlabHomeworkGeXingTotalNum = cast.ToInt64(val)
		}
		if val, exists := exam31["must_right_num"]; exists {
			resp.IlabHomeworkMustRightNum = cast.ToInt64(val)
		}
		if val, exists := exam31["must_is_submit"]; exists {
			resp.IlabHomeworkMustIsSubmit = cast.ToInt64(val)
		}
		if val, exists := exam31["must_submit_time"]; exists {
			resp.IlabHomeworkMustSubmitTime = cast.ToInt64(val)
		}
		if val, exists := exam31["must_participate_num"]; exists {
			resp.IlabHomeworkMustParticipateNum = cast.ToInt64(val)
		}
	}

	// 内部阶段测 (exam34)
	if exam34, ok := examAnswer["exam34"]; ok {
		if val, exists := exam34["is_submit"]; exists {
			resp.InnerStageIsSubmit = cast.ToInt64(val)
		}
		if val, exists := exam34["highest_score"]; exists {
			resp.InnerStageHighestScore = cast.ToInt64(val)
		}
		if val, exists := exam34["is_pass"]; exists {
			resp.InnerStageIsPass = cast.ToInt64(val)
		}
		if val, exists := exam34["is_full_score"]; exists {
			resp.InnerStageIsFullScore = cast.ToInt64(val)
		}
		if val, exists := exam34["exam_answer_detail"]; exists {
			resp.InnerStageAnswerDetail = val
		}
	}

	// 帮帮英语 - 单词练习 (exam14)
	if exam14, ok := examAnswer["exam14"]; ok {
		if val, exists := exam14["participate_num"]; exists {
			resp.WordpracticeParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam14["right_num"]; exists {
			resp.WordpracticeRightNum = cast.ToInt64(val)
		}
		if val, exists := exam14["total_num"]; exists {
			resp.WordpracticeTotalNum = cast.ToInt64(val)
		}
	}

	// 帮帮英语 - 单词学习 (exam15)
	if exam15, ok := examAnswer["exam15"]; ok {
		if val, exists := exam15["participate_num"]; exists {
			resp.WordlearnParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam15["right_num"]; exists {
			resp.WordlearnRightNum = cast.ToInt64(val)
		}
		if val, exists := exam15["total_num"]; exists {
			resp.WordlearnTotalNum = cast.ToInt64(val)
		}
	}

	// 帮帮英语 - 练一练 (exam17)
	if exam17, ok := examAnswer["exam17"]; ok {
		if val, exists := exam17["participate_num"]; exists {
			resp.PracticesParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam17["right_num"]; exists {
			resp.PracticesRightNum = cast.ToInt64(val)
		}
		if val, exists := exam17["total_num"]; exists {
			resp.PracticesTotalNum = cast.ToInt64(val)
		}
	}

	// 帮帮英语 - 出门测 (exam18)
	if exam18, ok := examAnswer["exam18"]; ok {
		if val, exists := exam18["participate_num"]; exists {
			resp.OutTestParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam18["right_num"]; exists {
			resp.OutTestRightNum = cast.ToInt64(val)
		}
		if val, exists := exam18["total_num"]; exists {
			resp.OutTestTotalNum = cast.ToInt64(val)
		}
	}

	// 帮帮英语 - 提升训练 (exam19)
	if exam19, ok := examAnswer["exam19"]; ok {
		if val, exists := exam19["participate_num"]; exists {
			resp.ImproveParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam19["right_num"]; exists {
			resp.ImproveRightNum = cast.ToInt64(val)
		}
		if val, exists := exam19["total_num"]; exists {
			resp.ImproveTotalNum = cast.ToInt64(val)
		}
	}

	// 帮帮英语 - 语法练习 (exam21)
	if exam21, ok := examAnswer["exam21"]; ok {
		if val, exists := exam21["participate_num"]; exists {
			resp.GrammarpracticeParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam21["right_num"]; exists {
			resp.GrammarpracticeRightNum = cast.ToInt64(val)
		}
		if val, exists := exam21["total_num"]; exists {
			resp.GrammarpracticeTotalNum = cast.ToInt64(val)
		}
	}

	// 帮帮英语 - 入门测 (exam23)
	if exam23, ok := examAnswer["exam23"]; ok {
		if val, exists := exam23["participate_num"]; exists {
			resp.InTestParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam23["right_num"]; exists {
			resp.InTestRightNum = cast.ToInt64(val)
		}
		if val, exists := exam23["total_num"]; exists {
			resp.InTestTotalNum = cast.ToInt64(val)
		}
	}

	// 浣熊英语 - 小试牛刀 (exam211)
	if exam211, ok := examAnswer["exam211"]; ok {
		if val, exists := exam211["score"]; exists {
			resp.TryKnifeScore = cast.ToInt64(val)
		}
		if val, exists := exam211["submit_time"]; exists {
			resp.TryKnifeSubmitTime = cast.ToInt64(val)
		}
		if val, exists := exam211["submit_status"]; exists {
			resp.TryKnifeSubmitStatus = cast.ToInt64(val)
		}
	}

	// 浣熊英语 - lesson1/2/3 (exam212)
	if exam212, ok := examAnswer["exam212"]; ok {
		if val, exists := exam212["lesson1_score"]; exists {
			resp.LessonOneScore = cast.ToInt64(val)
		}
		if val, exists := exam212["lesson1_submit_time"]; exists {
			resp.LessonOneSubmitTime = cast.ToInt64(val)
		}
		if val, exists := exam212["lesson1_submit_status"]; exists {
			resp.LessonOneSubmitStatus = cast.ToInt64(val)
		}
		if val, exists := exam212["lesson2_score"]; exists {
			resp.LessonTwoScore = cast.ToInt64(val)
		}
		if val, exists := exam212["lesson2_submit_time"]; exists {
			resp.LessonTwoSubmitTime = cast.ToInt64(val)
		}
		if val, exists := exam212["lesson2_submit_status"]; exists {
			resp.LessonTwoSubmitStatus = cast.ToInt64(val)
		}
		if val, exists := exam212["lesson3_score"]; exists {
			resp.LessonThreeScore = cast.ToInt64(val)
		}
		if val, exists := exam212["lesson3_submit_time"]; exists {
			resp.LessonThreeSubmitTime = cast.ToInt64(val)
		}
		if val, exists := exam212["lesson3_submit_status"]; exists {
			resp.LessonThreeSubmitStatus = cast.ToInt64(val)
		}
	}

	// 浣熊英语 - 能力挑战 (exam213)
	if exam213, ok := examAnswer["exam213"]; ok {
		if val, exists := exam213["score"]; exists {
			resp.PowerChallengeScore = cast.ToInt64(val)
		}
		if val, exists := exam213["submit_time"]; exists {
			resp.PowerChallengeSubmitTime = cast.ToInt64(val)
		}
		if val, exists := exam213["submit_status"]; exists {
			resp.PowerChallengeSubmitStatus = cast.ToInt64(val)
		}
	}

	// 浣熊英语 - 综合演练 (exam216)
	if exam216, ok := examAnswer["exam216"]; ok {
		if val, exists := exam216["score"]; exists {
			resp.SynthesisManoeuvreScore = cast.ToInt64(val)
		}
		if val, exists := exam216["submit_time"]; exists {
			resp.SynthesisManoeuvreSubmitTime = cast.ToInt64(val)
		}
		if val, exists := exam216["submit_status"]; exists {
			resp.SynthesisManoeuvreSubmitStatus = cast.ToInt64(val)
		}
	}

	// 浣熊英语 - 综合秀场 (exam217)
	if exam217, ok := examAnswer["exam217"]; ok {
		if val, exists := exam217["score"]; exists {
			resp.SynthesisShowScore = cast.ToInt64(val)
		}
		if val, exists := exam217["submit_time"]; exists {
			resp.SynthesisShowSubmitTime = cast.ToInt64(val)
		}
		if val, exists := exam217["submit_status"]; exists {
			resp.SynthesisShowSubmitStatus = cast.ToInt64(val)
		}
	}
}
