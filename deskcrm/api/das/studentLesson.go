package das

import (
	"deskcrm/api/zbcore"
	"deskcrm/conf"
	"deskcrm/util"
	"errors"
	"sync"

	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	ApiMaxNum = 50

	module = "das"
	entity = "studentLesson"
)

type StudentLessonInfo struct {
	StudentUid        int64 `json:"studentUid" mapstructure:"studentUid"`
	LessonId          int64 `json:"lessonId" mapstructure:"lessonId"`
	CourseId          int64 `json:"courseId" mapstructure:"courseId"`
	IsFinished        int   `json:"isFinished" mapstructure:"isFinished"`               // 是否完课
	IsAttended        int   `json:"isAttended" mapstructure:"isAttended"`               // 是否到课 5分钟
	IsAttendedLong    int   `json:"isAttendedLong" mapstructure:"isAttendedLong"`       // 是否到课 30分钟
	IsPlayback        int   `json:"isPlayback" mapstructure:"isPlayback"`               // 是否回放
	PlaybackDuration  int64 `json:"playbackDuration" mapstructure:"playbackDuration"`   // 回放时长（秒）
	AttendedDuration  int64 `json:"attendedDuration" mapstructure:"attendedDuration"`   // 到课时长（秒）
	HomeworkStatus    int   `json:"homeworkStatus" mapstructure:"homeworkStatus"`       // 课后作业状态（0未布置 1未提交 2提交未批改 3已批改）
	HomeworkRecorrect int   `json:"homeworkRecorrect" mapstructure:"homeworkRecorrect"` // 作业等订正状态
}

// 获取章节维度学生的字段
func getStudentLessonAllFields() []string {
	return []string{
		"studentUid",        // 学生ID
		"courseId",          // 课程ID
		"lessonId",          // 章节ID
		"isFinished",        // 是否完课
		"isAttended",        // 是否到课 5分钟
		"isAttendedLong",    // 是否到课 30分钟
		"isPlayback",        // 是否回放
		"playbackDuration",  // 回放时长
		"attendedDuration",  // 到课时长
		"micNum",            // 抢麦次数
		"praiseNum",         // 表扬次数
		"chatNum",           // 课中聊天次数
		"reviewStar",        // 课堂评价-星级
		"interactRightNum",  // 互动题答对数
		"interactAnswerNum", // 互动题回答数
		"interactTotalNum",  // 互动题应答总数
		"examRightNum",      // 课中测试答对数
		"examAnswerNum",     // 课中测试回答数
		"examTotalNum",      // 课中测试总数
		"homeworkStatus",    // 课后作业状态（0未布置 1未提交 2提交未批改 3已批改）
		"homeworkLevel",     // 作业等级ABCD
		"homeworkRecorrect", // 作业等订正状态
		"status",            // 状态(0:有效 1:无效)
		"deleteReason",      // 退课原因
		"startTime",         // 开始时间
	}
}

// 获取章节维度学生信息
func GetDasLessonData(ctx *gin.Context, studentUids []int64, lessonId int64, fields []string) (map[int64]map[int64]*StudentLessonInfo, error) {
	return GetDasLessonsData(ctx, studentUids, []int64{lessonId}, fields)
}

// 获取章节维度学生信息
func GetDasLessonsData(ctx *gin.Context, studentUids, lessonIds []int64, fields []string) (map[int64]map[int64]*StudentLessonInfo, error) {
	if len(studentUids) == 0 || len(lessonIds) == 0 {
		return nil, errors.New("param error")
	}

	arrStudentLessonIds := util.GetApi2SplitParam(studentUids, lessonIds, "_")

	if len(fields) == 0 {
		fields = getStudentLessonAllFields()
	} else {
		if isMatch, _ := fwyyutils.InArrayString("studentUid", fields); !isMatch {
			fields = append(fields, "studentUid")
		}

		if isMatch, _ := fwyyutils.InArrayString("lessonId", fields); !isMatch {
			fields = append(fields, "lessonId")
		}
	}

	if len(studentUids) > ApiMaxNum {
		// concurrency
		return getStudentLessonInBatch(ctx, arrStudentLessonIds, fields), nil
	} else {
		return getStudentLessonNotBatch(ctx, arrStudentLessonIds, fields)
	}
}

func getStudentLessonNotBatch(ctx *gin.Context, arrStudentLessonIds []string, fields []string) (map[int64]map[int64]*StudentLessonInfo, error) {
	res := make(map[int64]map[int64]*StudentLessonInfo, 0)
	arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, module, entity, "getKVByLessonId", false, conf.GetAppName())
	arrParams := map[string]interface{}{
		"studentLessonIds":    arrStudentLessonIds,
		"studentLessonFields": fields,
	}

	apiOut := make([]*StudentLessonInfo, 0)
	apiResp, err := zbcore.PostDas(ctx, arrParams, arrHeader, &apiOut)
	zlog.Debugf(ctx, "apiResp:%+v, err:%+v", apiResp, err)
	for _, item := range apiOut {
		if _, ok := res[item.StudentUid]; !ok {
			res[item.StudentUid] = make(map[int64]*StudentLessonInfo)
		}
		res[item.StudentUid][item.LessonId] = item
	}
	return res, err
}

func getStudentLessonInBatch(ctx *gin.Context, arrStudentLessonIds []string, fields []string) map[int64]map[int64]*StudentLessonInfo {
	chunks := fwyyutils.ChunkArrayString(arrStudentLessonIds, ApiMaxNum)
	wg := &sync.WaitGroup{}
	ch := make(chan map[int64]map[int64]*StudentLessonInfo)
	for _, chunk := range chunks {
		wg.Add(1)
		go func(uids []string) {
			defer wg.Done()

			singleRet, err := getStudentLessonNotBatch(ctx, arrStudentLessonIds, fields)
			if err != nil {
				zlog.Warnf(ctx, "getKVByStudentUids failed, studentUid:%+v, err:%s", uids, err)
				return
			}

			ch <- singleRet
		}(chunk)
	}

	result := make(map[int64]map[int64]*StudentLessonInfo)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer colWg.Done()
		for stuMap := range ch {
			for stuUid, item := range stuMap {
				if _, ok := result[stuUid]; !ok {
					result[stuUid] = make(map[int64]*StudentLessonInfo)
				}
				for lessonId, dataItem := range item {
					result[stuUid][lessonId] = dataItem
				}
			}
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()

	return result
}
