package dataQuery

import (
	"deskcrm/api/assistantdeskgo"
	"deskcrm/api/dataproxy"
	"slices"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// GetLuData 获取课程学生的LU数据
func (s *Singleton) GetLuData(ctx *gin.Context, courseId int64, studentUid int64) (map[int64]*dataproxy.GetLuDataResp, error) {
	if courseId == 0 || studentUid == 0 {
		return make(map[int64]*dataproxy.GetLuDataResp), nil
	}

	s.AddFields(ctx, DataSourceLu, []string{"lessonId"})
	fields, err := s.GetFields(ctx, DataSourceLu)
	if err != nil {
		return nil, err
	}

	client := dataproxy.NewClient()
	params := dataproxy.GetListByCourseIdsStudentUidsParam{
		CourseIds:   []int64{courseId},
		StudentUids: []int64{studentUid},
		Fields:      fields,
	}

	data, err := client.GetListByCourseIdsStudentUids(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetCommonLuDataByLessonStudents failed: %v", err)
		return nil, err
	}

	result := make(map[int64]*dataproxy.GetLuDataResp)
	for _, item := range data {
		result[item.LessonId] = item
	}

	return result, nil
}

func (s *Singleton) compatibleExamRelationData(ctx *gin.Context, data []*dataproxy.GetLuDataResp) error {
	fields, err := s.GetFields(ctx, DataSourceLu)
	if err != nil {
		return err
	}
	if !slices.Contains(fields, "exam_answer") || len(data) == 0 {
		return nil
	}

	// 提取lessonIds
	lessonIds := make([]int64, 0, len(data))
	for _, item := range data {
		lessonIds = append(lessonIds, item.LessonId)
	}

	// 去重
	lessonIdsMap := make(map[int64]bool)
	uniqueLessonIds := make([]int64, 0)
	for _, id := range lessonIds {
		if !lessonIdsMap[id] {
			lessonIdsMap[id] = true
			uniqueLessonIds = append(uniqueLessonIds, id)
		}
	}

	// 定义关系类型
	relationTypes := []int64{
		BindTypePracticeInClass,
		BindTypePreview,
		BindTypeHomework,
		BindTypeHomeworkIlab,
		BindTypeStage,
		BindTypeTestInClass,
		BindTypePrimaryMathPreview,
		BindTypePosttestMore,
		BindTypeOralQuestion,
		BindTypeWordPractice,
		BindTypeWordLearn,
		BindTypeOutTest,
		BindTypeImproveTest,
		BindTypeSyntasTest,
		BindTypePractises,
		BindTypeInTest,
	}

	// 查询试卷关联数据
	relationMaps := make(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)
	if len(uniqueLessonIds) > 0 {
		s.AddFields(ctx, DataSourceExamRelation, []string{"bind_id", "relation_type", "total_num"})

		relationData, err := s.GetExamRelationData(ctx, uniqueLessonIds, RelationTypeLesson, relationTypes)
		if err != nil {
			zlog.Warnf(ctx, "GetExamRelationData failed: %v", err)
			return err
		}

		// 构建映射关系
		for bindId, relationMap := range relationData {
			if relationMaps[bindId] == nil {
				relationMaps[bindId] = make(map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)
			}
			for relationType, examInfo := range relationMap {
				relationMaps[bindId][relationType] = examInfo
			}
		}

		// 处理数据
		for i, info := range data {
			lessonId := info.LessonId

			if info.ExamAnswer == nil {
				info.ExamAnswer = make(map[string]map[string]interface{})
			}

			// 互动题
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypePracticeInClass] != nil {
				data[i].ExerciseTotalNum = relationMaps[lessonId][BindTypePracticeInClass].TotalNum
				if info.ExamAnswer["exam1"] == nil {
					info.ExamAnswer["exam1"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam1"]["total_num"] = relationMaps[lessonId][BindTypePracticeInClass].TotalNum
			}

			// 课前预习 小学预习 小学课前预习
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypePreview] != nil {
				data[i].PreviewTotalNum5 = relationMaps[lessonId][BindTypePreview].TotalNum
				if info.ExamAnswer["exam5"] == nil {
					info.ExamAnswer["exam5"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam5"]["total_num"] = relationMaps[lessonId][BindTypePreview].TotalNum
			}

			// 课后作业【巩固练习】
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeHomework] != nil {
				data[i].HomeworkPracticeTotalNum = relationMaps[lessonId][BindTypeHomework].TotalNum
				if info.ExamAnswer["exam7"] == nil {
					info.ExamAnswer["exam7"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam7"]["total_num"] = relationMaps[lessonId][BindTypeHomework].TotalNum
			}

			// ilab巩固练习升级绑
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeHomeworkIlab] != nil {
				data[i].IlabHomeworkGeXingTotalNum = relationMaps[lessonId][BindTypeHomeworkIlab].TotalNum
				if info.ExamAnswer["exam31"] == nil {
					info.ExamAnswer["exam31"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam31"]["total_num"] = relationMaps[lessonId][BindTypeHomeworkIlab].TotalNum
			}

			// 阶段性测试
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeStage] != nil {
				data[i].StageTestExamTotalNum = relationMaps[lessonId][BindTypeStage].TotalNum
				if info.ExamAnswer["exam9"] == nil {
					info.ExamAnswer["exam9"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam9"]["total_num"] = relationMaps[lessonId][BindTypeStage].TotalNum
			}

			// 堂堂测
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeTestInClass] != nil {
				data[i].TangTangExamTotalNum = relationMaps[lessonId][BindTypeTestInClass].TotalNum
				if info.ExamAnswer["exam10"] == nil {
					info.ExamAnswer["exam10"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam10"]["total_num"] = relationMaps[lessonId][BindTypeTestInClass].TotalNum
			}

			// 小学-数学-同步练习
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypePrimaryMathPreview] != nil {
				data[i].SynchronousPracticeTotalNum = relationMaps[lessonId][BindTypePrimaryMathPreview].TotalNum
				if info.ExamAnswer["exam11"] == nil {
					info.ExamAnswer["exam11"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam11"]["total_num"] = relationMaps[lessonId][BindTypePrimaryMathPreview].TotalNum
			}

			// 初高中预习测试
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypePosttestMore] != nil {
				data[i].PreviewTotalNum13 = relationMaps[lessonId][BindTypePosttestMore].TotalNum
				if info.ExamAnswer["exam13"] == nil {
					info.ExamAnswer["exam13"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam13"]["total_num"] = relationMaps[lessonId][BindTypePosttestMore].TotalNum
			}

			// 口述题
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeOralQuestion] != nil {
				data[i].HaveOralQuestion = relationMaps[lessonId][BindTypeOralQuestion].BindStatus
				data[i].OralQuestionTotalNum = relationMaps[lessonId][BindTypeOralQuestion].TotalNum
				if info.ExamAnswer["exam32"] == nil {
					info.ExamAnswer["exam32"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam32"]["is_have"] = relationMaps[lessonId][BindTypeOralQuestion].BindStatus
				info.ExamAnswer["exam32"]["total_num"] = relationMaps[lessonId][BindTypeOralQuestion].TotalNum
			}

			// 单词练习
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeWordPractice] != nil {
				data[i].WordpracticeTotalNum = relationMaps[lessonId][BindTypeWordPractice].TotalNum
				if info.ExamAnswer["exam14"] == nil {
					info.ExamAnswer["exam14"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam14"]["total_num"] = relationMaps[lessonId][BindTypeWordPractice].TotalNum
			}

			// 单词学习
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeWordLearn] != nil {
				data[i].WordlearnTotalNum = relationMaps[lessonId][BindTypeWordLearn].TotalNum
				if info.ExamAnswer["exam15"] == nil {
					info.ExamAnswer["exam15"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam15"]["total_num"] = relationMaps[lessonId][BindTypeWordLearn].TotalNum
			}

			// 出门测试
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeOutTest] != nil {
				if info.ExamAnswer["exam18"] == nil {
					info.ExamAnswer["exam18"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam18"]["total_num"] = relationMaps[lessonId][BindTypeOutTest].TotalNum
			}

			// 提升训练
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeImproveTest] != nil {
				data[i].ImproveTotalNum = relationMaps[lessonId][BindTypeImproveTest].TotalNum
				if info.ExamAnswer["exam19"] == nil {
					info.ExamAnswer["exam19"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam19"]["total_num"] = relationMaps[lessonId][BindTypeImproveTest].TotalNum
			}

			// 语法练习
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeSyntasTest] != nil {
				data[i].GrammarpracticeTotalNum = relationMaps[lessonId][BindTypeSyntasTest].TotalNum
				if info.ExamAnswer["exam21"] == nil {
					info.ExamAnswer["exam21"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam21"]["total_num"] = relationMaps[lessonId][BindTypeSyntasTest].TotalNum
			}

			// 练一练
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypePractises] != nil {
				data[i].PracticesTotalNum = relationMaps[lessonId][BindTypePractises].TotalNum
				if info.ExamAnswer["exam17"] == nil {
					info.ExamAnswer["exam17"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam17"]["total_num"] = relationMaps[lessonId][BindTypePractises].TotalNum
			}

			// 入门测
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeInTest] != nil {
				data[i].InTestTotalNum = relationMaps[lessonId][BindTypeInTest].TotalNum
				if info.ExamAnswer["exam23"] == nil {
					info.ExamAnswer["exam23"] = make(map[string]interface{})
				}
				info.ExamAnswer["exam23"]["total_num"] = relationMaps[lessonId][BindTypeInTest].TotalNum
			}
		}
	}

	return nil
}

// GetLuData 获取课程学生的公共LU数据
func (s *Singleton) GetCommonLuData(ctx *gin.Context, lessonIds []int64, studentUid int64) (map[int64]*dataproxy.GetCommonLuResp, error) {
	if len(lessonIds) == 0 || studentUid == 0 {
		return make(map[int64]*dataproxy.GetCommonLuResp), nil
	}

	fields, err := s.GetFields(ctx, DataSourceCommonLu)
	if err != nil {
		return nil, err
	}

	client := dataproxy.NewClient()
	params := dataproxy.GetLuComonListByStudentLessonsParam{
		LessonIds:  lessonIds,
		StudentUid: studentUid,
		Fields:     fields,
	}

	data, err := client.GetLuComonListByStudentLessons(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetCommonLuDataByLessonStudents failed: %v", err)
		return nil, err
	}

	result := make(map[int64]*dataproxy.GetCommonLuResp)
	for _, item := range data {
		result[item.LessonId] = item
	}

	return result, nil
}

// GetExamRelationData 获取试卷绑定数据
func (s *Singleton) GetExamRelationData(ctx *gin.Context, bindIds []int64, bindType int64, relationTypes []int64) (map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp, error) {
	if len(bindIds) == 0 || bindType == 0 {
		return make(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp), nil
	}

	fields, err := s.GetFields(ctx, DataSourceExamRelation)
	if err != nil {
		return nil, err
	}

	client := dataproxy.NewClient()

	params := dataproxy.GetListByBindIdsBindTypeRelationTypesCommonParam{
		BindIds:       bindIds,
		BindType:      bindType,
		RelationTypes: relationTypes,
		Fields:        fields,
	}

	data, err := client.GetListByBindIdsBindTypeRelationTypes(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetExamRelationData failed: %v", err)
		return nil, err
	}

	result := make(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)
	for _, item := range data {
		result[item.BindId][item.RelationType] = item
	}

	return result, nil
}

func (s *Singleton) GetLearnReportByClueGradeIds(ctx *gin.Context, clueIdGradeIdMap map[string]int64) (getLessonListResp map[string]assistantdeskgo.LearnReportInfo, err error) {
	leadsInfoMap, err := assistantdeskgo.NewClient().GetLearnReportByClueGradeIds(ctx, clueIdGradeIdMap)
	if err != nil {
		zlog.Warnf(ctx, "GetPrivateLeadsData failed: %s", err.Error())
		return leadsInfoMap, nil
	}
	return leadsInfoMap, nil
}

// GetLessonDataByLessonIds 获取章节数据
func (s *Singleton) GetLessonDataByLessonIds(ctx *gin.Context, lessonIds []int64) (map[int64]*dataproxy.GetLessonDataByLessonIdsResp, error) {
	if len(lessonIds) == 0 {
		return make(map[int64]*dataproxy.GetLessonDataByLessonIdsResp), nil
	}

	fields, err := s.GetFields(ctx, DataSourceLesson)
	if err != nil {
		return nil, err
	}

	data, err := dataproxy.NewClient().GetLessonDataByLessonIds(ctx, dataproxy.GetLessonDataByLessonIdsParam{
		LessonIds: lessonIds,
		Fields:    fields,
	})
	if err != nil {
		return nil, err
	}

	result := make(map[int64]*dataproxy.GetLessonDataByLessonIdsResp)
	for _, item := range data {
		result[item.LessonId] = item
	}

	return result, nil
}
